# Role: Central configuration file for the Spring Boot application
# Purpose:
# Configures server port (8081)
# Sets up MCP server properties (name, version, type, capabilities)
# Configures API endpoints for communication with chat-mcp
# Sets up logging levels


spring.application.name=mcp_microservice_chatboot_ai

# Server configuration
server.port=8081

# OpenAI configuration
openai.api-key=********************************************************************************************************************************************************************
openai.model=gpt-4o
openai.temperature=0.7
openai.max-tokens=2000
openai.timeout=60

# Spring AI OpenAI configuration
spring.ai.openai.api-key=********************************************************************************************************************************************************************
spring.ai.openai.model=gpt-4o
spring.ai.openai.temperature=0.7
spring.ai.openai.max-tokens=2000

# Backend Configuration
# Options: CHAT_MCP, WIN_MCP
mcp.backend.type=CHAT_MCP

# Tool Selection Strategy
# Options: SMART (calls all endpoints), SPECIFIC (calls only relevant endpoints)
mcp.tool.strategy=SPECIFIC

# Cache Configuration
# Enable/disable caching for performance optimization
mcp.cache.enabled=true

# Chat-MCP API configuration
chat-mcp.api.base-url=http://localhost:8080/api
chat-mcp.api.url=http://localhost:8080/api
chat-mcp.api.auth-endpoint=/auth/login
chat-mcp.api.messages-endpoint=/messages
chat-mcp.api.conversations-endpoint=/conversations
chat-mcp.api.user-data-endpoint=/users/data

# Win-MCP API configuration
win-mcp.api.base-url=http://localhost:8082
win-mcp.api.url=http://localhost:8082
win-mcp.api.auth-endpoint=/auth/login
win-mcp.api.tenant-auth-endpoint=/auth/tenant/login
win-mcp.api.user-data-endpoint=/api/winplus/user-data

# Win-MCP Authentication Configuration
# Authentication type: DUAL (tenant + user) or SINGLE (user only)
win-mcp.auth.type=DUAL
win-mcp.auth.tenant.username=0001
win-mcp.auth.tenant.password=123456

# Logging
logging.level.com.example.mcp_microservice_chatboot_ai=DEBUG
logging.level.org.springframework.web=INFO

# Logging configuration
logging.level.com.theokanning.openai=DEBUG

# Server address to bind to all network interfaces
server.address=0.0.0.0

# ==================== CACHING CONFIGURATION ====================
# Cache Configuration for Performance Optimization
spring.cache.type=caffeine
spring.cache.caffeine.spec=maximumSize=1000,expireAfterWrite=30m

# Cache TTL Configuration (in seconds)
# Static data (products, suppliers) - 2 hours
cache.ttl.static-data=7200
# Semi-static data (client info) - 30 minutes
cache.ttl.semi-static-data=1800
# Dynamic data (sales, transactions) - 10 minutes
cache.ttl.dynamic-data=600
# Authentication tokens - 1 hour
cache.ttl.auth-tokens=3600

# Cache Size Limits
cache.size.max-entries=2000
cache.size.auth-tokens=500
cache.size.static-data=1000
cache.size.dynamic-data=1500

# Cache Metrics (for monitoring)
management.metrics.cache.instrument=true

# Cache Warmup Configuration
mcp.cache.warmup.enabled=true

# Performance Monitoring
mcp.performance.monitoring.enabled=true
mcp.performance.logging.interval=300000